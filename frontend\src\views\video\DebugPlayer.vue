<template>
  <div class="debug-player">
    <h3>本地视频调试播放器</h3>
    <p>
      该播放器用于测试本地视频文件是否能正常播放。请确保视频文件
      <code>stream0.mp4</code> 已经放置在 <code>frontend/public/videos/</code> 目录下。
    </p>
    <video controls autoplay muted name="media" class="video-element">
      <!-- 
        在 Vite 中, public 目录下的文件会被服务在根路径.
        因此, frontend/public/videos/stream0.mp4 可以通过 /videos/stream0.mp4 访问。
        添加 muted 属性可以解决某些浏览器因自动播放策略而阻止播放的问题。
      -->
      <source src="./stream0.mp4" type="video/mp4" />
      您的浏览器不支持 video 标签。
    </video>
    <p class="info">如果视频无法播放，请检查：<br/>
      1. 视频文件 <code>stream0.mp4</code> 是否已放置在 <code>frontend/public/videos/</code> 目录中。<br/>
      2. 浏览器控制台是否有错误信息。
    </p>
  </div>
</template>

<script setup>
// No script logic needed for this simple component
</script>

<style scoped>
.debug-player {
  padding: 20px;
  border: 1px solid #ccc;
  margin-top: 20px;
  background-color: #f9f9f9;
}
.video-element {
  width: 100%;
  max-width: 600px;
  margin-top: 10px;
}
.info {
    margin-top: 15px;
    font-size: 14px;
    color: #666;
}
</style>